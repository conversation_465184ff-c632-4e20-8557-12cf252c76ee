# Flutter Scaffold 项目配置文件详细说明

本文档详细说明了 Flutter Scaffold 项目中所有重要配置文件的作用和配置说明。

## 📋 目录

- [项目管理配置](#项目管理配置)
- [构建和代码生成配置](#构建和代码生成配置)
- [开发工具配置](#开发工具配置)
- [平台特定配置](#平台特定配置)
- [代码质量配置](#代码质量配置)
- [部署和CI/CD配置](#部署和cicd配置)

---

## 🏗️ 项目管理配置

### `melos.yaml` - Monorepo 管理配置
**位置：** 根目录
**作用：** 管理多包 Flutter 项目的工具配置

```yaml
name: flutter_scaffold
packages:
  - apps/**      # 应用程序包
  - packages/**  # 共享包

scripts:
  analyze: melos exec -- flutter analyze --fatal-infos
  format: melos exec -- dart format --set-exit-if-changed .
  test: melos exec -- flutter test --coverage
  gen: melos exec -- dart run build_runner build --delete-conflicting-outputs
```

**核心功能：**
- 统一管理多个包的依赖
- 批量执行命令（分析、测试、格式化）
- 版本管理和发布协调
- 工作空间依赖解析

### `pubspec.yaml` 文件系列
**作用：** Dart/Flutter 包的依赖和配置管理

#### 根目录 `pubspec.yaml`
```yaml
name: flutter_scaffold_workspace
environment:
  sdk: ^3.8.1
dev_dependencies:
  melos: ^3.2.0
```
- 定义工作空间根配置
- 管理 melos 工具依赖

#### `apps/mobile/pubspec.yaml`
- **应用程序主配置**
- 定义应用依赖、资源、字体
- 配置多环境支持（development/staging/production）
- 管理本地包依赖（data_models, ui_library）

#### `packages/*/pubspec.yaml`
- **共享包配置**
- 定义包的公共API和依赖
- 配置代码生成工具

---

## 🔧 构建和代码生成配置

### `build.yaml` - 代码生成全局配置
**位置：** 根目录
**作用：** 统一配置所有代码生成工具的行为

```yaml
targets:
  $default:
    builders:
      freezed:
        options:
          union_key: runtimeType
          union_value_case: snake
      json_serializable:
        options:
          explicit_to_json: true
          include_if_null: false
```

**配置说明：**
- **Freezed 配置：** Union 类型序列化规则
- **JSON 序列化：** 排除 null 值，显式 toJson 调用
- **Injectable：** 自动依赖注册
- **Retrofit：** API 客户端生成选项

### `Mason.yaml` - 代码模板配置
**位置：** 根目录
**作用：** 管理代码生成模板（bricks）

```yaml
name: flutter_scaffold
vars:
  author: "Flutter Scaffold Team"
  email: "<EMAIL>"

bricks:
  feature: { path: bricks/feature }
  model: { path: bricks/model }
  bloc: { path: bricks/bloc }
  page: { path: bricks/page }
```

**支持的模板（精简版）：**
- `feature` - 完整功能模块（Clean Architecture 三层 + API客户端）
- `page` - 页面组件（主题集成 + BLoC 支持）
- `widget` - 可复用组件（主题集成）
- `validator` - 数据验证器（业务规则 + 国际化）
- `adr` - 架构决策记录

---

## 🛠️ 开发工具配置

### `mise.toml` - 开发环境管理
**位置：** 根目录和 `apps/mobile/`
**作用：** 管理开发工具版本和任务

```toml
[tools]
flutter = "3.32.5-stable"
java = "23.0.2"

[tasks.dev-android]
alias = "dev-and"
description = "run for android"
run = "flutter run --no-sound-null-safety -d M2012K11AC"
depends = ['pub-get']
```

**功能：**
- 统一开发环境版本
- 定义常用开发任务
- 管理工具链依赖

### `Makefile` - 开发工作流自动化
**位置：** 根目录
**作用：** 提供便捷的开发命令

```makefile
setup:
    dart pub global activate melos
    dart pub global activate mason_cli
    melos bootstrap

run-dev:
    cd $(APP_DIR) && flutter run --flavor development -t lib/main_development.dart
```

**主要命令：**
- `make setup` - 环境初始化
- `make run-dev/stag/prod` - 多环境运行
- `make test` - 测试执行
- `make gen` - 代码生成

---

## 📱 平台特定配置

### Android 配置

#### `android/build.gradle.kts` - 项目级构建配置
```kts
allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
```

#### `android/app/build.gradle.kts` - 应用级构建配置
```kts
android {
    namespace = "com.example.flutter_scaffold_mobile"
    compileSdk = flutter.compileSdkVersion

    productFlavors {
        create("development") {
            applicationIdSuffix = ".dev"
        }
        create("staging") {
            applicationIdSuffix = ".staging"
        }
        create("production") {
            // 生产环境配置
        }
    }
}
```

**功能：**
- 多环境构建配置
- 应用签名和版本管理
- 依赖库配置

---

## 🔍 代码质量配置

### `analysis_options.yaml` - 静态分析配置
**位置：** 各包目录
**作用：** 配置 Dart 代码分析规则

```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"

linter:
  rules:
    - use_decorated_box
    - prefer_const_constructors
```

**分层配置：**
- **apps/mobile：** 使用 `flutter_lints`，严格规则
- **packages/ui_library：** 使用 `flutter_lints`，UI 优化规则
- **packages/data_models：** 使用 `lints`，基础规则

### `.cspell.json` - 拼写检查配置
**位置：** 根目录
**作用：** 配置代码拼写检查规则

```json
{
  "version": "0.2",
  "language": "en",
  "words": [
    "flutter", "dart", "pubspec", "freezed", "injectable"
  ],
  "ignorePaths": [
    "**/*.g.dart",
    "**/*.freezed.dart"
  ]
}
```

### `.gitignore` - 版本控制忽略配置
**位置：** 根目录
**作用：** 定义不需要版本控制的文件

```gitignore
# Flutter/Dart
.dart_tool/
build/
**/*.g.dart
**/*.freezed.dart

# Environment
.env*

# IDE
.vscode/
.idea/
```

### `.dartignore` - Dart 分析忽略配置
**位置：** 根目录
**作用：** 定义 Dart 分析器忽略的文件

```
bricks/
**/__brick__/
**/*.g.dart
**/*.freezed.dart
```

---

## 🚀 部署和CI/CD配置

### `fastlane/Fastfile` - 自动化部署配置
**位置：** `apps/mobile/fastlane/`
**作用：** 配置应用发布流程

```ruby
platform :android do
  desc "Deploy to Google Play Store"
  lane :deploy do
    gradle(task: "bundle", build_type: "Release", flavor: "Production")
    upload_to_play_store(track: "internal")
  end

  desc "Deploy to Firebase App Distribution"
  lane :firebase_deploy do
    gradle(task: "assemble", build_type: "Release", flavor: "Staging")
    firebase_app_distribution()
  end
end
```

**支持的部署目标：**
- Google Play Store
- Firebase App Distribution
- 自定义分发渠道

---

## 📚 文档和指南

### 项目文档结构
```
docs/
├── DEVELOPMENT_GUIDE.md          # 开发指南
├── CLEAN_ARCHITECTURE_GUIDE_PRO.md  # 架构指南
├── STATE_MANAGEMENT_GUIDE.md     # 状态管理指南
├── UI_GUIDE_PRO.md              # UI 开发指南
├── MASON_GUIDE.md               # 代码生成指南
└── adr/                         # 架构决策记录
```

### 特殊文件说明

#### `BRICKS.md` - Mason 模板使用说明
详细说明各种代码模板的使用方法和最佳实践。

#### `UPGRADE_GUIDE.md` - 升级指南
记录项目升级过程中的重要变更和迁移步骤。

#### `app-dictionary.txt` - 项目词典
包含项目特定术语，用于拼写检查和文档生成。

---

## 🔧 配置文件最佳实践

### 1. 版本管理
- 所有工具版本在 `mise.toml` 中统一管理
- 依赖版本使用语义化版本约束
- 定期更新和测试依赖兼容性

### 2. 环境隔离
- 使用 Flutter flavors 区分环境
- 环境特定配置通过 `.env` 文件管理
- 敏感信息不提交到版本控制

### 3. 代码质量
- 统一的 linter 规则配置
- 自动化代码格式化
- 强制性静态分析检查

### 4. 构建优化
- 合理配置代码生成选项
- 优化构建缓存策略
- 分层构建配置管理

---

## 📝 维护建议

1. **定期审查配置文件**：确保配置与项目需求保持一致
2. **文档同步更新**：配置变更时及时更新相关文档
3. **版本兼容性测试**：升级工具版本前进行充分测试
4. **团队培训**：确保团队成员理解各配置文件的作用

---

## 🔍 配置文件快速参考

### 按功能分类的配置文件

| 功能类别 | 配置文件 | 位置 | 主要作用 |
|---------|---------|------|---------|
| **项目管理** | `melos.yaml` | 根目录 | Monorepo 管理 |
| | `pubspec.yaml` | 各包目录 | 依赖管理 |
| **代码生成** | `build.yaml` | 根目录 | 构建配置 |
| | `Mason.yaml` | 根目录 | 模板管理 |
| **开发工具** | `mise.toml` | 根目录/apps/mobile | 环境管理 |
| | `Makefile` | 根目录 | 工作流自动化 |
| **代码质量** | `analysis_options.yaml` | 各包目录 | 静态分析 |
| | `.cspell.json` | 根目录 | 拼写检查 |
| | `.gitignore` | 根目录 | 版本控制 |
| | `.dartignore` | 根目录 | Dart 分析 |
| **平台配置** | `build.gradle.kts` | android/ | Android 构建 |
| | `Podfile` | ios/ | iOS 依赖 |
| **部署** | `Fastfile` | fastlane/ | 自动化部署 |

### 配置文件依赖关系

```mermaid
graph TD
    A[melos.yaml] --> B[pubspec.yaml]
    B --> C[build.yaml]
    C --> D[analysis_options.yaml]
    E[Mason.yaml] --> F[bricks/]
    G[mise.toml] --> H[Makefile]
    I[Fastfile] --> J[build.gradle.kts]
```

### 常见配置问题和解决方案

#### 1. 依赖冲突
**问题：** 不同包之间的依赖版本冲突
**解决：** 在根目录 `pubspec.yaml` 中使用 `dependency_overrides`

#### 2. 代码生成失败
**问题：** build_runner 执行失败
**解决：** 检查 `build.yaml` 配置，清理 `.dart_tool` 目录

#### 3. 环境配置不一致
**问题：** 团队成员开发环境差异
**解决：** 统一使用 `mise.toml` 管理工具版本

#### 4. 静态分析错误
**问题：** 不同包的 linter 规则冲突
**解决：** 根据包类型配置不同的 `analysis_options.yaml`

---

## 📋 配置检查清单

### 新项目设置
- [ ] 配置 `melos.yaml` 包路径
- [ ] 设置各包的 `pubspec.yaml`
- [ ] 配置 `build.yaml` 代码生成选项
- [ ] 设置 `mise.toml` 工具版本
- [ ] 配置 `analysis_options.yaml` 规则
- [ ] 设置 `.gitignore` 和 `.dartignore`
- [ ] 配置平台特定构建文件

### 定期维护
- [ ] 更新依赖版本
- [ ] 审查 linter 规则
- [ ] 检查构建配置
- [ ] 更新工具版本
- [ ] 同步文档

---

*最后更新：2025-01-01*
*维护者：Flutter Scaffold Team*
